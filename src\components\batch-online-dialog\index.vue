<template>
  <el-dialog
    v-loading="loading"
    width="920px"
    :title="title"
    append-to-body
    class="batch-online-dialog"
    :visible.sync="display"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :element-loading-text="loadingText"
  >
    <div class="batch-online-content">
      <!-- 流程列表 -->
      <div class="batch-online-flow__out">
        <div class="batch-online-flow__head">流程列表</div>
        <ul class="batch-online-flow">
          <li
            v-for="(el, index) in flowList"
            :key="el.id"
            class="batch-online-flow__item"
            :class="{
              'batch-online-flow__item--highLight': activeId === el.id
            }"
            @click="handleFlowClick(el.id, index)"
          >
            <span class="text" :title="el.jobName">{{ el.jobName }}</span>
            <el-tag v-if="batchConfig[el.id].isComplete" size="mini" type="success">已完成</el-tag>
          </li>
        </ul>
      </div>
      <!-- 配置表单 -->
      <div v-loading="!isInit" class="batch-online-config">
        <config-form
          v-if="isInit"
          ref="configRef"
          :key="`${activeId}${Date.now}`"
          :title.sync="title"
          :flow-id="activeId"
          :is-batch="true"
          :data="form"
          :cluster="clusterList"
        />
      </div>
    </div>
    <!-- 底部 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="display = false">取消</el-button>
      <el-button type="primary" @click="submit(false)">保存</el-button>
      <el-button type="primary" @click="submit(true)">启动</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Watch } from 'vue-property-decorator';
import { PaBase } from '@/common/pipeace-base';
import { flowDefaultConfig } from '@/utils';
import { get, post, put } from '@/apis/utils/net';
import { URL_JOB_PREONLINE, URL_JOBRES_UPDATE_LIST, URL_RES_FLINK } from '@/apis/commonApi';
import { batchOperate } from '@/apis/flowNewApi';
import store from '@/store';
import { RELOAD_JOB } from '@/store/event-names/mutations';
import { cloneDeep, throttle } from 'lodash';

@Component({
  components: {
    ConfigForm: () => import('../single-flow-config/index.vue')
  }
})
export default class BatchOnlineDialog extends PaBase {
  @Prop({ type: String, default: '' }) id!: string;
  @Prop({ type: String, default: '' }) projectId!: string;
  @Prop({ type: String, default: '' }) type!: string;
  @Prop({ type: Array, default: () => [] }) list!: any[];
  @Prop({ type: Boolean, default: false }) status!: boolean;
  @PropSync('show', { type: Boolean, default: true }) display!: boolean;
  @Ref('configRef') readonly configRef!: any;
  private title = '流程配置';
  private flowList: any[] = [];
  private activeId = '';
  private batchConfig = {};

  private loading = false;
  private loadingText = '正在进行数据校验，请稍等...';
  private jobData: any = {};
  private originProperties = '';
  private submit = throttle(this.handleSubmit, 1200);
  clusterList = [];
  isInit = false;
  get form() {
    return this.batchConfig[this.activeId];
  }

  get orgId() {
    return this.form.orgId;
  }

  get disabled() {
    return ['INPROD', 'PROD'].includes(this.jobData.jobStatus);
  }

  @Watch('list', { immediate: true })
  handleListChange(val: any[]) {
    if (val.length < 1) {
      return;
    }
    this.flowList = cloneDeep(val);
    this.flowList.forEach(({ id, properties, jobName, jobType, memo, orgId, jobStatus }: any) => {
      try {
        this.$set(this.batchConfig, id, {
          ...flowDefaultConfig(),
          ...{
            ...JSON.parse(properties),
            jobName,
            jobType,
            memo,
            orgId,
            isComplete: true,
            jobStatus
          }
        });
      } catch (e) {
        this.$set(this.batchConfig, id, {
          ...flowDefaultConfig(),
          ...{
            jobName,
            jobType,
            memo,
            orgId,
            isComplete: false,
            jobStatus
          }
        });
      }
    });
    this.activeId = this.flowList[0].id;
    this.jobData = this.flowList[0];
  }
  async created() {
    // 获取集群列表
    const { data } = await get(URL_RES_FLINK, { orgId: this.$store.state.userInfo.orgId });
    this.clusterList = data || [];
    this.isInit = true;
  }
  async handleFlowClick(id: string, index) {
    try {
      const res = await this.configRef.submit(id);
      res.isComplete = true;
      this.$set(this.batchConfig, this.activeId, res);
      this.jobData = this.flowList[index];
      this.activeId = id;
    } catch (e) {
      const [[{ message }]] = Object.values(e);
      this.$tip.error(message || '配置有误，请检查右侧配置');
    }
  }

  async handleSubmit(needRun = false) {
    try {
      this.loading = true;
      this.loadingText = '正在进行数据校验，请稍等...';
      const res = await this.configRef.submit();
      res.isComplete = true;
      this.$set(this.batchConfig, this.activeId, res);
      await this.verifyFlow(this.getParameter('preOnline'));
      this.loadingText = `正在${needRun ? '处理' : '更新'}数据，请稍等...`;
      await this.save(needRun);
      this.loading = false;
    } catch (e) {
      this.loading = false;
      this.$tip.error('请完成当前流程配置');
      console.log(e);
    }
  }

  /* 流程批量检测 */
  async verifyFlow(list: any[], index = 0) {
    try {
      this.activeId = list[index];
      await this.sleep(100);
      const res = await this.configRef.submit();
      this.batchConfig[list[index]] = cloneDeep(res);
      const count = index + 1;
      return count < list.length ? this.verifyFlow(list, count) : Promise.resolve(true);
    } catch (e) {
      this.activeId = list[index];
      return Promise.reject(e);
    }
  }

  sleep(time: number) {
    return new Promise((resolve) => {
      const timer = setTimeout(() => {
        clearTimeout(timer);
        resolve(true);
      }, time);
    });
  }

  /* 获取参数 */
  getParameter(mode: string): any[] {
    const { flowList, batchConfig, status } = this;
    if (mode === 'preOnline') {
      return flowList.map(({ id }) => id);
    }
    if (mode === 'save') {
      return flowList.map(({ id }) => {
        const projectId = this.projectId;
        const temp = cloneDeep(batchConfig[id]);
        delete temp.property;
        delete temp.isComplete;
        const { jobName, memo } = temp;
        const properties = JSON.stringify(temp);
        return { projectId, id, jobName, memo, properties };
      });
    }
    if (mode === 'online') {
      return flowList.map((n: any) => {
        return {
          id: n.id,
          fromLastCheckpoint: status
        };
      });
    }
    return [];
  }

  /* 数据保存 */
  async save(needRun = false) {
    try {
      const { success, msg } = await put(URL_JOBRES_UPDATE_LIST, this.getParameter('save'));
      if (success) {
        this.originProperties = JSON.stringify(this.getParameter('save'));
        if (needRun) {
          await this.online();
          return;
        }
        this.$tip.success(msg);
        this.loading = false;
        this.closeDialog();
        return;
      }
      this.loading = false;
      this.$tip.error(msg);
    } catch (e) {
      this.loading = false;
    }
  }

  /* 上线 */
  async online() {
    if (this.originProperties === JSON.stringify(this.getParameter('save'))) {
      try {
        const res = await post(URL_JOB_PREONLINE, {
          relationBatchTag: false,
          jobs: this.getParameter('preOnline')
        });
        if (res.success) {
          const jobIds = this.getParameter('preOnline');
          const { success, msg, data } = await batchOperate({
            jobIds,
            batchEventType: 'ONLINE',
            fromState: this.status, // status参数表示是否基于状态启动
            saveState: false
          });
          if (typeof (this.$parent as any)?.fetchList === 'function') {
            (this.$parent as any).fetchList();
          }
          if (success) {
            this.loading = false;
            this.$tip.success(msg || '启动成功，，可点击批量操作信息按钮查看');
            if (this.$store.state.job.data.id) {
              store.commit(RELOAD_JOB, true);
            }
            this.closeDialog();
            return;
          }
          this.loading = false;
          this.$tip.errorPro(msg, data);
          return;
        }
        this.loading = false;
        this.$tip.errorPro(res.msg, res.data);
        return;
      } catch (e) {
        this.loading = false;
        return;
      }
    }
    this.loading = false;
    this.$tip.warning('请先保存再运行');
  }

  closeDialog() {
    this.$emit('close');
    this.display = false;
  }
}
</script>

<style lang="scss" scoped>
.batch-online {
  /* 弹窗 */
  &-dialog {
    top: 50%;
    left: 50%;
    right: unset;
    bottom: unset;
    transform: translate(-50%, -50%);

    ::v-deep .el-dialog {
      margin: auto !important;

      &__body {
        padding: 0 10px;
      }
    }
  }

  /* 内容 */
  &-content {
    display: flex;
    justify-content: space-between;
    width: 900px;
    height: 450px;
  }

  /* 流程 */
  &-flow__out {
    width: 260px;
    height: 100%;
    border-right: 2px solid #f1f1f1;
    box-sizing: border-box;
  }

  &-flow__head {
    height: 40px;
    line-height: 40px;
    padding-left: 20px;
    border-bottom: 1px solid #f1f1f1;
  }

  &-flow {
    padding: 10px;
    height: calc(100% - 40px);
    overflow-y: auto;
    overflow-x: hidden;

    &__item {
      display: flex;
      align-items: center;
      height: 40px;
      line-height: 40px;
      box-sizing: border-box;
      cursor: pointer;
      border-radius: 5px;
      text-align: left;
      padding: 0 10px 0 16px;

      & > .text {
        flex: 1;
        margin-right: 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &--highLight {
        color: #409eff !important;
        font-weight: normal !important;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }

      &--finish {
        color: #54c958;
        font-weight: bold;
      }
    }
  }

  /* 配置 */
  &-config {
    width: 630px;
    height: 100%;
    overflow: auto;
  }
}
</style>
