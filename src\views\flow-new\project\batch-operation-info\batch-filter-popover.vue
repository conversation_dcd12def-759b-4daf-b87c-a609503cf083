<template>
  <el-popover
    ref="popover"
    v-model="showPopover"
    placement="bottom"
    width="250"
    trigger="click"
    class="batch-filter-popover"
    popper-class="batch-filter-popover-content"
  >
    <!-- 筛选内容区域 -->
    <div class="batch-filter-popover__content">
      <!-- 批操作类型筛选 -->
      <div class="batch-filter-popover__selections">
        <span class="batch-filter-popover__selections--label">批操作类型</span>
        <bs-select
          v-model="filterParams.batchEventType"
          class="batch-filter-popover__selections--select"
          clearable
          placeholder="全部"
          :options="batchEventTypeOptions"
        />
      </div>

      <!-- 批操作状态筛选 -->
      <div class="batch-filter-popover__selections">
        <span class="batch-filter-popover__selections--label">批操作状态</span>
        <bs-select
          v-model="filterParams.batchEventStatus"
          class="batch-filter-popover__selections--select"
          clearable
          placeholder="全部"
          :options="batchEventStatusOptions"
        />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="batch-filter-popover__header">
      <el-button @click="handleSearch(true)">重置</el-button>
      <el-button type="primary" @click="handleSearch(false, true)"> 搜索 </el-button>
    </div>

    <!-- 触发按钮 -->
    <el-button
      slot="reference"
      size="small"
      icon="iconfont icon-guolv"
      :class="[filtered ? 'batch-filter-popover__button--filtered' : '']"
    >
      筛选
    </el-button>
  </el-popover>
</template>

<script lang="ts">
import { Vue, Component, PropSync, Prop } from 'vue-property-decorator';

@Component
export default class BatchFilterPopover extends Vue {
  @PropSync('visible') showPopover!: boolean;
  @Prop({ default: () => [] }) batchEventTypeOptions!: any[];
  @Prop({ default: () => [] }) batchEventStatusOptions!: any[];

  filterParams: any = {
    batchEventType: '',
    batchEventStatus: ''
  };

  get filtered() {
    const { filterParams } = this;
    return filterParams.batchEventType || filterParams.batchEventStatus;
  }

  handleSearch(isClear = false, shouldClosePopover = true) {
    if (isClear) {
      this.filterParams = {
        batchEventType: '',
        batchEventStatus: ''
      };
      this.$emit('search', this.filterParams);
      return;
    }

    if (shouldClosePopover) {
      this.showPopover = false;
    }

    this.$emit('search', { ...this.filterParams });
  }
}
</script>

<style lang="scss" scoped>
.batch-filter-popover {
  &__content {
    width: 100%;
    box-sizing: border-box;
  }
  &__selections {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;

    &--label {
      font-size: 14px;
      font-weight: 500;
      color: #606266;
      line-height: 20px;
      margin-bottom: 8px;
    }

    &--select {
      ::v-deep .el-select {
        width: 100% !important;
      }

      ::v-deep .el-input {
        width: 100% !important;
      }

      ::v-deep .el-input__inner {
        width: 100% !important;
      }
    }
  }

  &__header {
    text-align: right;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;

    .el-button {
      min-width: 70px;
    }
  }

  &__button {
    &--filtered {
      background-color: #409eff;
      color: white;
      border-color: #409eff;
    }
  }
}
</style>
