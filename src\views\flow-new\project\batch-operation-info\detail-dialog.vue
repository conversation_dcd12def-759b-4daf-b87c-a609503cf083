<template>
  <bs-dialog
    title="批量操作详情"
    :visible.sync="dialogVisible"
    size="large"
    :before-close="handleClose"
  >
    <!-- 弹框内容 -->
    <div class="batch-detail-dialog">
      <!-- 表格区域 -->
      <div class="batch-detail-dialog__content">
        <!-- 搜索区域 -->
        <div class="batch-detail-dialog__search">
          <!-- 流程名称搜索 -->
          <bs-search
            v-model.trim="searchParams.jobName"
            placeholder="请输入流程名称"
            maxlength="50"
            style="width: 200px; margin-right: 12px"
            @search="handleSearch"
          />

          <!-- 操作结果筛选 -->
          <bs-select
            v-model="searchParams.operationSuccess"
            placeholder="操作结果"
            clearable
            style="width: 120px; margin-right: 12px"
            :options="operationSuccessOptions"
            @change="handleSearch"
          />

          <!-- 重置按钮 -->
          <el-button @click="handleReset">重置</el-button>
        </div>

        <!-- 表格 -->
        <bs-table
          v-loading="tableLoading"
          :data="tableData.tableData"
          class="batch-detail-dialog__table"
          height="400px"
          row-key="jobId"
          :column-data="tableData.columnData"
          :page-data="tableData.pageData"
          :column-settings="false"
          @page-change="handleCurrentChange"
        />
      </div>
    </div>
  </bs-dialog>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { getBatchOperationDetailList } from '@/apis/flowNewApi';

interface IBatchDetailSearchParams {
  batchEventId: string; // 批操作ID
  jobName?: string; // 流程名称，支持模糊搜索
  operationSuccess?: string; // 操作结果：SUCCESS/FAILED/UNKNOWN
}

@Component
export default class BatchDetailDialog extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop() batchData!: any; // 批量操作的基本信息

  tableLoading = false;

  // 表格配置
  tableData: any = {
    columnData: [],
    tableData: [],
    pageData: {
      pageSize: 10,
      currentPage: 1,
      total: 0,
      layout: 'total, prev, pager, next'
    }
  };

  searchParams: IBatchDetailSearchParams = {
    batchEventId: '', // 批操作ID
    jobName: '', // 流程名称
    operationSuccess: '' // 操作结果
  };

  sortData: any = { order: 'DESC', prop: 'createTime' };

  // 操作结果选项
  operationSuccessOptions = [
    { label: '成功', value: 'SUCCESS' },
    { label: '失败', value: 'FAILED' },
    { label: '未知', value: 'UNKNOWN' }
  ];

  get dialogVisible() {
    return this.visible;
  }

  set dialogVisible(val: boolean) {
    this.$emit('update:visible', val);
  }

  @Watch('visible')
  onVisibleChange(val: boolean) {
    if (val && this.batchData) {
      this.searchParams.batchEventId = this.batchData.batchEventId;
      this.searchParams.jobName = '';
      this.searchParams.operationSuccess = '';
      this.getBatchDetailList();
    }
  }

  // 获取批量操作详情列表
  async getBatchDetailList() {
    try {
      this.tableLoading = true;

      // 构建搜索参数，只传递有值的参数
      const search: any = {
        batchEventId: this.searchParams.batchEventId
      };
      if (this.searchParams.jobName) {
        search.jobName = this.searchParams.jobName;
      }
      if (this.searchParams.operationSuccess) {
        search.operationSuccess = this.searchParams.operationSuccess;
      }

      const params = {
        pageData: {
          pageSize: this.tableData.pageData.pageSize,
          currentPage: this.tableData.pageData.currentPage,
          total: this.tableData.pageData.total
        },
        search
      };

      const { data, success, msg, error } = await getBatchOperationDetailList(params);

      if (success) {
        const { columnData, tableData, pageData } = data;

        columnData[0].width = 110;
        columnData[1].width = 150;
        columnData[5].width = 160;
        columnData[6].width = 160;

        // 处理枚举值转换和时间格式化
        const processedTableData = tableData.map((row: any) => {
          const processedRow = { ...row };
          columnData.forEach((column: any) => {
            if (
              column.dataType === 'Enum' &&
              column.enumData &&
              processedRow[column.value] !== undefined
            ) {
              // 将枚举值转换为对应的显示文本
              processedRow[column.value] =
                column.enumData[processedRow[column.value]] || processedRow[column.value];
            } else if (
              column.dataType === 'Date' &&
              processedRow[column.value] !== undefined &&
              processedRow[column.value] !== null
            ) {
              // 将时间戳转换为日期时间格式
              processedRow[column.value] = this.formatDateTime(processedRow[column.value]);
            }
          });
          return processedRow;
        });

        this.tableData = {
          columnData,
          tableData: processedTableData,
          pageData: {
            ...pageData,
            layout: 'total, prev, pager, next'
          }
        };
      } else {
        this.$message.error(error || msg);
      }
    } catch (e) {
      this.$message.error('获取批量操作详情失败');
    } finally {
      this.tableLoading = false;
    }
  }

  // 刷新列表
  refreshList() {
    this.getBatchDetailList();
  }

  // 格式化时间戳为日期时间字符串
  formatDateTime(timestamp: number): string {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  }

  // 搜索处理
  handleSearch() {
    (this.tableData.pageData as any).currentPage = 1;
    this.getBatchDetailList();
  }

  // 重置搜索条件
  handleReset() {
    this.searchParams.jobName = '';
    this.searchParams.operationSuccess = '';
    (this.tableData.pageData as any).currentPage = 1;
    this.getBatchDetailList();
  }

  // 分页处理
  handleCurrentChange(currentPage: number, pageSize: number) {
    this.tableData.pageData.currentPage = currentPage;
    this.tableData.pageData.pageSize = pageSize;
    this.getBatchDetailList(); // 重新获取数据
  }

  // 关闭弹框
  handleClose() {
    this.dialogVisible = false;
    // 重置数据
    this.tableData.tableData = [];
    (this.tableData.pageData as any).currentPage = 1;
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0;
}

.batch-detail-dialog {
  &__table {
    width: 100%;
  }

  &__search {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-radius: 4px;
    padding-left: 16px;
    padding-right: 16px;
  }
}
</style>
