import { vm } from '@/main';
const router = [
  {
    path: '/flowProject',
    name: 'flowProject',
    meta: {
      title: '流程设计',
      access: 'PA.FLOW',
      icon: 'iconfont icon-l<PERSON><PERSON><PERSON><PERSON>'
    },
    component: () => import('../views/flow-new/project/index.vue')
  },
  {
    path: '/flow',
    name: 'flowNew',
    meta: {
      title: '流程管理',
      access: 'PA.FLOW.FLOW_MGR.VIEW',
      noMenu: true
    },
    beforeEnter(to, from, next) {
      to.meta.title = to.query.title;
      next();
    },
    component: () => import('../views/flow-new/design/index.vue')
  },
  {
    path: '/flowVersion',
    name: 'flowVersion',
    component: () => import('../views/flow-new/design/version-detail.vue')
  },
  {
    path: '/batchOperationInfo',
    name: 'batchOperationInfo',
    meta: {
      title: '批量操作信息',
      access: 'PA.FLOW.FLOW_MGR.VIEW',
      noMenu: true
    },
    component: () => import('../views/flow-new/project/batch-operation-info/index.vue')
  }
];

export { router };
