/*
 * @Description:
 * @Author: ranran
 * @Date: 2020-01-13 10:29:00
 * @LastEditTime: 2022-06-28 13:43:45
 * @LastEditors: Please set LastEditors
 */
const path = require('path');
const resolve = (dir) => path.join(__dirname, dir);

const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const target = 'http://************:34000/';
module.exports = {
  lintOnSave: 'error',
  productionSourceMap: false,
  transpileDependencies: ['cube-compute-mgr-ui', '@svgdotjs'],
  css: {
    sourceMap: process.env.NODE_ENV === 'production' ? false : true,
    extract: false,
    loaderOptions: {
      scss: {
        prependData: `@import "@/style/bsui-variables.scss";`
      }
    }
  },
  configureWebpack: {
    plugins: [
      new MonacoWebpackPlugin({
        languages: ['java', 'javascript', 'json', 'sql']
      }),
      new CopyWebpackPlugin([
        {
          from: 'node_modules/bs-ui-pro/lib/theme-chalk/fonts',
          to: 'fonts/',
          toType: 'dir'
        },
        {
          from: 'node_modules/bs-ui-pro/lib/theme-chalk/default-index.css'
        }
      ])
    ]
  },

  chainWebpack(config) {
    config.module.rule('svg').exclude.add(resolve('src/assets/svg')).end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/svg'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end();
  },
  devServer: {
    proxy: {
      '^/portal': {
        target,
        changeOrigin: true,
        ws: true
      },
      '/api': {
        target,
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/api': ''
        }
      },
      '/cluster': {
        target,
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/cluster': '/bs/portal/cluster'
        }
      },
      '/charts': {
        target,
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/charts': '/bs/portal/charts'
        }
      }
    }
  },
  publicPath: './',
  outputDir: 'dist/static',
  indexPath: 'pipeace.html'
};
