<template>
  <pro-page class="batch-operation-info" title="批量操作信息" :fixed-header="false">
    <!-- 页面头部操作区域 -->
    <div slot="operation" class="batch-operation-info__header">
      <!-- 时间范围筛选 -->
      <el-date-picker
        v-model="timeRange"
        type="datetimerange"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        format="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss"
        style="width: 350px"
        @change="handleTimeRangeChange"
      />

      <!-- 筛选popover -->
      <batch-filter-popover
        ref="batchFilterPopover"
        :visible.sync="filterPopoverVisible"
        :batch-event-type-options="batchEventTypeOptions"
        :batch-event-status-options="batchEventStatusOptions"
        @search="handleFilter"
      />
    </div>

    <!-- 表格内容 -->
    <div class="batch-operation-info__content">
      <bs-table
        v-loading="tableLoading"
        :data="tableData.tableData"
        class="batch-operation-info__table"
        height="calc(100vh - 285px)"
        :column-data="tableData.columnData"
        :page-data="tableData.pageData"
        row-key="id"
        @page-change="handleCurrentChange"
        @refresh="refreshList"
      >
        <!-- 批操作状态 -->
        <template slot="batchEventStatus" slot-scope="{ row }">
          <el-tag size="mini" :type="getStatusTagType(row.batchEventStatus)">
            {{ getStatusText(row.batchEventStatus) }}
          </el-tag>
        </template>

        <!-- 批操作类型 -->
        <template slot="batchEventType" slot-scope="{ row }">
          <el-tag size="mini" :type="getActionTagType(row.batchEventType)">
            {{ getActionText(row.batchEventType) }}
          </el-tag>
        </template>

        <!-- 操作栏 -->
        <template slot="operation" slot-scope="{ row }">
          <el-tooltip effect="light" content="查看详情" placement="top">
            <i class="iconfont icon-chakan" @click="viewDetail(row)"></i>
          </el-tooltip>
        </template>
      </bs-table>
    </div>

    <!-- 详情弹框 -->
    <detail-dialog :visible.sync="detailDialogVisible" :batch-data="selectedBatchData" />
  </pro-page>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { getBatchOperationList } from '@/apis/flowNewApi';
import DetailDialog from './detail-dialog.vue';
import BatchFilterPopover from './batch-filter-popover.vue';

interface IBatchSearchParams {
  batchEventType?: string; // 批操作类型
  batchEventStatus?: string; // 批操作状态
  batchEventSubmitTimeStart?: string; // 批操作提交时间开始
  batchEventSubmitTimeEnd?: string; // 批操作提交时间结束
}

@Component({
  components: {
    DetailDialog,
    BatchFilterPopover
  }
})
export default class BatchOperationInfo extends Vue {
  tableLoading = false;
  detailDialogVisible = false;
  selectedBatchData: any = null;
  filterPopoverVisible = false;
  timeRange: string[] = [];

  pageSize = this.$store.getters.pageSize || 25;

  // 表格配置
  tableData: any = {
    columnData: [],
    tableData: [],
    pageData: { pageSize: this.pageSize, currentPage: 1, total: 0 }
  };

  searchParams: IBatchSearchParams = {};

  sortData: any = { order: 'DESC', prop: 'createTime' };

  // 从接口动态获取的选项数据
  batchEventTypeOptions: any[] = [];
  batchEventStatusOptions: any[] = [];

  created() {
    this.getBatchOperationList();
  }

  activated() {
    this.refreshList();
  }

  // 获取批量操作信息列表
  async getBatchOperationList(isRefresh = false) {
    try {
      this.tableLoading = true;

      // 构建搜索参数，只传递有值的参数
      const search: any = {};
      if (this.searchParams.batchEventType) {
        search.batchEventType = this.searchParams.batchEventType;
      }
      if (this.searchParams.batchEventStatus) {
        search.batchEventStatus = this.searchParams.batchEventStatus;
      }
      if (this.searchParams.batchEventSubmitTimeStart) {
        search.batchEventSubmitTimeStart = this.searchParams.batchEventSubmitTimeStart;
      }
      if (this.searchParams.batchEventSubmitTimeEnd) {
        search.batchEventSubmitTimeEnd = this.searchParams.batchEventSubmitTimeEnd;
      }

      const params = {
        pageData: this.tableData.pageData,
        search
      };

      const { data, success, msg, error } = await getBatchOperationList(params);

      if (success) {
        const { columnData, tableData, pageData } = data;

        // 处理时间格式化（保留枚举原始值，由模板处理显示）
        const processedTableData = tableData.map((row: any) => {
          const processedRow = { ...row };
          columnData.forEach((column: any) => {
            if (
              column.dataType === 'Date' &&
              processedRow[column.value] !== undefined &&
              processedRow[column.value] !== null
            ) {
              // 将时间戳转换为日期时间格式
              processedRow[column.value] = this.formatDateTime(processedRow[column.value]);
            }
            // 枚举值保持原始值，由模板中的slot处理显示
          });
          return processedRow;
        });

        // 设置时间列的宽度并提取枚举数据
        columnData.forEach((column: any) => {
          if (column.value === 'batchEventSubmitTime') {
            column.width = 160; // 批操作提交时间
          } else if (column.value === 'batchEventFinishTime') {
            column.width = 160; // 批操作完成时间
          }

          // 提取枚举数据用于筛选选项
          if (column.dataType === 'Enum' && column.enumData) {
            if (column.value === 'batchEventType') {
              this.batchEventTypeOptions = Object.keys(column.enumData).map((key) => ({
                label: column.enumData[key],
                value: key
              }));
            } else if (column.value === 'batchEventStatus') {
              this.batchEventStatusOptions = Object.keys(column.enumData).map((key) => ({
                label: column.enumData[key],
                value: key
              }));
            }
          }
        });

        // 添加操作列
        columnData.push({
          label: '操作',
          value: 'operation',
          width: 60,
          fixed: 'right',
          showOverflowTooltip: false
        });

        this.tableData = {
          columnData,
          tableData: processedTableData,
          pageData
        };

        isRefresh && this.$message.success('刷新成功');
      } else {
        this.$message.error(error || msg);
      }

      this.tableLoading = false;
    } catch (e) {
      this.tableLoading = false;
      this.$message.error('获取批量操作信息失败');
    }
  }

  // 时间范围变化处理
  handleTimeRangeChange(value: string[]) {
    if (value && value.length === 2) {
      // 转换为时间戳格式
      this.searchParams.batchEventSubmitTimeStart = new Date(value[0]).getTime().toString();
      this.searchParams.batchEventSubmitTimeEnd = new Date(value[1]).getTime().toString();
    } else {
      delete this.searchParams.batchEventSubmitTimeStart;
      delete this.searchParams.batchEventSubmitTimeEnd;
    }
    (this.tableData.pageData as any).currentPage = 1;
    this.getBatchOperationList();
  }

  // 筛选处理
  handleFilter(filterParams: any) {
    // 更新筛选参数，只设置有值的参数
    if (filterParams.batchEventType) {
      this.searchParams.batchEventType = filterParams.batchEventType;
    } else {
      delete this.searchParams.batchEventType;
    }

    if (filterParams.batchEventStatus) {
      this.searchParams.batchEventStatus = filterParams.batchEventStatus;
    } else {
      delete this.searchParams.batchEventStatus;
    }

    (this.tableData.pageData as any).currentPage = 1;
    this.getBatchOperationList();
  }

  // 刷新列表
  refreshList() {
    this.getBatchOperationList(true);
  }

  // 分页处理
  handleCurrentChange(currentPage: number, pageSize: number) {
    (this.tableData.pageData as any).currentPage = currentPage;
    (this.tableData.pageData as any).pageSize = pageSize;
    this.getBatchOperationList();
  }

  // 查看详情
  viewDetail(row: any) {
    this.selectedBatchData = row;
    this.detailDialogVisible = true;
  }

  // 获取批操作状态文本（从动态数据中获取）
  getStatusText(status: string): string {
    const statusOption = this.batchEventStatusOptions.find((option) => option.value === status);
    return statusOption ? statusOption.label : status;
  }

  // 获取批操作类型文本（从动态数据中获取）
  getActionText(action: string): string {
    const actionOption = this.batchEventTypeOptions.find((option) => option.value === action);
    return actionOption ? actionOption.label : action;
  }

  // 获取状态tag类型
  getStatusTagType(status: string): string {
    const typeMap = {
      SUBMITTED: 'primary',
      RUNNING: 'warning',
      FINISHED: 'success',
      FAILED: 'danger'
    };
    return typeMap[status] || 'info';
  }

  // 获取操作类型tag类型
  getActionTagType(action: string): string {
    const typeMap = {
      PUB: 'primary',
      DEV: 'warning',
      ONLINE: 'success',
      OFFLINE: 'danger'
    };
    return typeMap[action] || 'info';
  }

  // 格式化时间戳为日期时间字符串
  formatDateTime(timestamp: number): string {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  }
}
</script>

<style lang="scss" scoped>
.batch-operation-info {
  &__header {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;

    .el-date-editor {
      margin: 0;
    }
  }

  &__content {
    height: calc(100vh - 250px);
  }

  &__table {
    width: 100%;
  }

  ::v-deep .bs-pro-page__header .bs-pro-page__header-operation .el-input {
    width: 278px;
  }

  ::v-deep .bs-pro-page__header .bs-pro-page__header-operation .bs-search {
    width: 278px !important;
  }

  .iconfont {
    cursor: pointer;
    &.disabled-icon {
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }
}

.batch-cancel-btn {
  margin-left: 12px;
  font-size: 14px;
}
</style>
