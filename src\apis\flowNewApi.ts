import { get, del, post, postFile, put } from './utils/net';
// 获取某个项目下的所有流程（带筛选项）
export const getFlowList = (data: unknown) => {
  return post('rs/pa/job/searchJob', data);
};

// 根据流程id获取流程详情
export const getFlowById = (data: unknown) => {
  return get('/rs/pa/job/findById', data);
};

// 根据id获取流程版本信息
export const getFlowVersionId = (data: unknown) => {
  return get('/rs/pa/job/getJobByJobVersionId', data);
};

//流程版本回滚
export const versionBack = (data: unknown) => {
  return get('/rs/pa/job/rollBack', data);
};

// 新建流程
export const addFlow = (data: unknown) => {
  return post('/rs/pa/job/add', data);
};

// 更新流程
export const updateFlow = (data: unknown) => {
  return put('/rs/pa/job/update', data);
};

// 项目列表搜索接口
export const getProjectList = (data: unknown) => {
  return post(`/rs/pa/project/projectList`, data);
};
// 服务列表接口
export const getServiceResList = () => {
  return get('/rs/pa/res/resList');
};
// 删除项目
export const deleteProject = (data: unknown) => {
  return del('/rs/pa/project/deleteById', data);
};

// 批量删除流程
export const deleteFlows = (data: unknown) => {
  return del('rs/pa/job/deleteByIds', {}, { data });
};

// 获取某个项目下的所有流程（带筛选项、项目接口）
export const getFlowListBy = (data: unknown) => {
  return post('/rs/pa/job/listJob', data);
};

// 导出流程
export const exportFlows = (data: unknown) => {
  return postFile('/rs/pa/job/export', data);
};

// 预发布流程
export const perPublishFlows = (data: unknown) => {
  return post('/rs/pa/job/prePublish', data);
};
// 发布流程
export const publishFlows = (data: unknown) => {
  return post('/rs/pa/job/publish', data);
};

// 取消发布流程
export const cancelPublish = (data: unknown) => {
  return post('/rs/pa/job/cancelPublish', data);
};

// 流程预启动
export const preOnline = (data: unknown) => {
  return post('/rs/pa/job/preOnline', data);
};

// 流程启动
export const online = (data: unknown) => {
  return post('/rs/pa/job/online', data);
};

// 流程停止
export const offline = (data: unknown) => {
  return post('/rs/pa/job/offline', data);
};

// 流程复制
export const batchCopy = (data: unknown) => {
  return post('/rs/pa/job/batchCopy', data);
};

// 流程移动
export const batchMove = (data: unknown) => {
  return post('/rs/pa/job/batchMove', data);
};

// 流程校验：compile\publish\test\code
export const validateComponent = (data: unknown) => {
  return post('/rs/pa/job/validateComponent', data);
};

// 服务详情中的引用流程的批量操作
export const batchOperate = (data: unknown) => {
  return post('/rs/pa/job/batchOperate', data);
};

// 服务详情中引用流程批量操作后的结果
export const batchOperateDetails = (data: unknown) => {
  return post('/rs/pa/job/batchOperateDetails', data);
};

// 批量操作列表
export const getBatchOperationList = (data: unknown) => {
  return post('/rs/pa/job/batchOperateList', data);
};

// 批量操作详情列表
export const getBatchOperationDetailList = (data: unknown) => {
  return post('/rs/pa/job/batchOperateJobList', data);
};
